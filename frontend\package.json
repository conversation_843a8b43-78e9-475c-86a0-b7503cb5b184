{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NEXT_TELEMETRY_DISABLED=1 next dev", "dev:clean": "npm run clean && cross-env NEXT_TELEMETRY_DISABLED=1 next dev", "build": "next build", "build:clean": "npm run clean && next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next && rimraf node_modules/.cache", "clean:all": "rimraf .next && rimraf node_modules/.cache && rimraf node_modules && npm install"}, "dependencies": {"@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "critters": "^0.0.23", "next": "14.2.15", "postcss": "^8.4.31", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "tailwindcss": "^3.4.17", "typescript": "^5"}, "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-next": "14.2.15", "rimraf": "^6.0.1"}}